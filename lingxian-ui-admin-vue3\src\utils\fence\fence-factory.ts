/**
 * 围栏工厂类
 * 封装所有围栏创建和操作方法
 */

import { Circle, Rect, Polygon, IText, Line } from 'fabric'
import { canvasToImageWithDPR, type ScreenInfo } from '@/store/modules/canvas/drawing'
import { Iposition } from '@/api/master/floor/type'
import { FENCE_CONFIG } from './fence-config'
import { FenceUtils } from './fence-utils'
import type { FenceColor } from './fence-types'

/**
 * 围栏工厂类
 * 封装所有围栏创建和操作方法，从FabricCanvas中迁移而来
 */
export class FenceFactory {
  private canvas: any
  private fenceColor: FenceColor
  private polygonDrawing: boolean = false
  private realtimeDimensionTexts: any[] = []
  private polygonPoints: { x: number; y: number }[] = [] // 保存多边形点
  private polygonPreviewLine: any = null // 多边形预览线
  private polygonPreviewShape: any = null // 多边形预览形状

  constructor(canvas: any) {
    this.canvas = canvas
    this.fenceColor = { ...FENCE_CONFIG.DEFAULT_FENCE_COLOR }
  }

  /**
   * 获取围栏颜色配置
   */
  getFenceColor(): FenceColor {
    return this.fenceColor
  }

  /**
   * 设置围栏颜色配置
   */
  setFenceColor(color: Partial<FenceColor>): void {
    this.fenceColor = { ...this.fenceColor, ...color }
  }

  /**
   * 添加圆形围栏
   */
  addCircleFence(center: { x: number; y: number }, radius: number): Circle {
    const zoomStyle = FenceUtils.getZoomAdaptedStyle(this.canvas)
    
    const circle = new Circle({
      left: center.x,
      top: center.y,
      radius: radius,
      fill: this.fenceColor.color,
      stroke: this.fenceColor.stroke,
      strokeWidth: zoomStyle.strokeWidth,
      originX: 'center',
      originY: 'center',
      lockUniScaling: true, // 锁定等比例缩放，确保圆形保持圆形
      ...FENCE_CONFIG.COMMON_FENCE_STYLE as any // 使用类型断言解决类型不兼容问题
    })
    
    this.canvas.add(circle)
    return circle
  }

  /**
   * 添加矩形围栏
   */
  addRectangleFence(position: { x: number; y: number }, width: number, height: number): Rect {
    const zoomStyle = FenceUtils.getZoomAdaptedStyle(this.canvas)

    const rectangle = new Rect({
      left: position.x,
      top: position.y,
      width: width,
      height: height,
      fill: this.fenceColor.color,
      stroke: this.fenceColor.stroke,
      strokeWidth: zoomStyle.strokeWidth,
      ...FENCE_CONFIG.COMMON_FENCE_STYLE as any // 使用类型断言解决类型不兼容问题
    })
    
    this.canvas.add(rectangle)
    return rectangle
  }

  /**
   * 创建多边形围栏
   */
  createPolygonFence(points: { x: number; y: number }[]): Polygon | null {
    if (!points || points.length < 3) return null
    
    const zoomStyle = FenceUtils.getZoomAdaptedStyle(this.canvas)
    
    const polygon = new Polygon(points, {
      fill: this.fenceColor.color,
      stroke: this.fenceColor.stroke,
      strokeWidth: zoomStyle.strokeWidth,
      ...FENCE_CONFIG.COMMON_FENCE_STYLE as any // 使用类型断言解决类型不兼容问题
    })
    
    this.canvas.add(polygon)
    return polygon
  }
  
  /**
   * 添加多边形点
   * @param point 新的多边形点坐标
   */
  addPolygonPoint(point: { x: number; y: number }): boolean {
    if (!this.polygonDrawing) {
      this.startPolygonDrawing()
    }
    
    this.polygonPoints.push(point)
    this.updatePolygonPreview()
    return true
  }
  
  /**
   * 开始多边形绘制
   */
  private startPolygonDrawing(): void {
    this.polygonDrawing = true
    this.polygonPoints = []
    this.clearPolygonPreview()
  }
  
  /**
   * 更新多边形预览
   */
  private updatePolygonPreview(): void {
    this.clearPolygonPreview()
    
    if (this.polygonPoints.length < 2) return
    
    // 创建预览线条
    const zoomStyle = FenceUtils.getZoomAdaptedStyle(this.canvas)
    
    // 绘制已确定的线段
    for (let i = 0; i < this.polygonPoints.length - 1; i++) {
      const line = new Line([
        this.polygonPoints[i].x, this.polygonPoints[i].y,
        this.polygonPoints[i + 1].x, this.polygonPoints[i + 1].y
      ], {
        stroke: this.fenceColor.stroke,
        strokeWidth: zoomStyle.strokeWidth,
        strokeDashArray: [5, 5],
        selectable: false,
        evented: false
      })
      
      this.canvas.add(line)
      if (!this.polygonPreviewLine) this.polygonPreviewLine = []
      this.polygonPreviewLine.push(line)
    }
  }
  
  /**
   * 清理多边形预览
   */
  private clearPolygonPreview(): void {
    if (this.polygonPreviewLine) {
      if (Array.isArray(this.polygonPreviewLine)) {
        this.polygonPreviewLine.forEach(line => this.canvas.remove(line))
      } else {
        this.canvas.remove(this.polygonPreviewLine)
      }
      this.polygonPreviewLine = null
    }
    
    if (this.polygonPreviewShape) {
      this.canvas.remove(this.polygonPreviewShape)
      this.polygonPreviewShape = null
    }
  }
  
  /**
   * 完成多边形绘制
   * @param lastPoint 最后一个点（可选）
   * @returns 创建的多边形对象，如果点数不足则返回null
   */
  finishPolygonDrawing(lastPoint?: { x: number; y: number }): Polygon | null {
    // 如果提供了最后一个点，检查是否与第一个点接近
    if (lastPoint && this.polygonPoints.length > 0) {
      const firstPoint = this.polygonPoints[0]
      const distToFirst = FenceUtils.calculateDistance(lastPoint, firstPoint)
      
      // 如果距离较远，添加最后一个点；否则自动闭合
      if (distToFirst > 10) {
        this.polygonPoints.push(lastPoint)
      }
    }
    
    // 确保至少有3个点
    if (this.polygonPoints.length < 3) {
      console.warn('多边形至少需要3个点')
      this.clearPolygonPreview()
      this.polygonDrawing = false
      this.polygonPoints = []
      return null
    }
    
    // 创建最终的多边形
    const polygon = this.createPolygonFence(this.polygonPoints)
    if (polygon) {
      polygon.set({
        id: 'fence',
        selectable: false,
        evented: false
      })
    }
    
    // 清理预览
    this.clearPolygonPreview()
    
    // 重置状态
    const resultPoints = [...this.polygonPoints]
    this.polygonDrawing = false
    this.polygonPoints = []
    
    console.log('多边形绘制完成，点数:', resultPoints.length)
    this.canvas.renderAll()
    
    return polygon
  }

  /**
   * 获取当前多边形绘制状态
   */
  isPolygonDrawing(): boolean {
    return this.polygonDrawing
  }

  /**
   * 取消多边形绘制
   */
  cancelPolygonDrawing(): void {
    this.clearPolygonPreview()
    this.polygonDrawing = false
    this.polygonPoints = []
  }

  /**
   * 获取当前多边形点数
   */
  getPolygonPointsCount(): number {
    return this.polygonPoints.length
  }
}

