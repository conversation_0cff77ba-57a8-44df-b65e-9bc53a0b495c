/**
 * 可编辑围栏类
 * 支持通过点击文本编辑尺寸，围栏会实时更新
 */

import { IText, Line, Group } from 'fabric'
import FabricCanvas from '@/utils/FabricCanvas'
import { Iposition } from '@/api/master/floor/type'
import { type ScreenInfo } from '@/store/modules/canvas/drawing'
import { FenceFactory } from './fence-factory'
import { FenceUtils } from './fence-utils'
import { convertToFenceData } from './fence-core'
import type { FenceDate, FenceType, DimensionType } from './fence-types'

// 导入多边形绘制器（假设存在）
declare class RealtimePolygonDrawer {
  constructor(canvas: any, fenceFactory: FenceFactory)
  renderPolygon(points: { x: number; y: number }[]): any
}

/**
 * 为文本元素添加通用的编辑事件处理
 * @param textObj 文本对象
 * @param canvas 画布对象
 * @param onEditCallback 编辑完成回调
 */
function addCommonTextEditEvents(textObj: IText, canvas: any, onEditCallback?: (newValue: number) => void) {
  // 保存原始值以便恢复
  const originalValue = (textObj as any).originalValue || parseFloat(textObj.text)
  
  // 监听文本变化
  canvas.on('text:changed', (opt: any) => {
    if (opt.target !== textObj) return
    
    const newText = opt.target.text
    // 验证输入是否为有效数字
    if (/^[0-9]*\.?[0-9]*$/.test(newText) || newText === '') {
      // 解析新的数值
      const newValue = parseFloat(newText)
      
      // 验证数值是否有效
      if (isNaN(newValue) || newValue <= 0) {
        // 无效数值，恢复原始值
        textObj.set({ text: FenceUtils.formatValue(originalValue) })
        canvas.renderAll()
        return
      }
      
      // 应用新值（四舍五入到两位小数）
      const formattedValue = FenceUtils.formatValue(newValue)
      textObj.set({ text: formattedValue })
      
      // 调用回调函数更新围栏尺寸
      if (onEditCallback) {
        onEditCallback(newValue)
      }
    } else {
      // 恢复到上一个有效值
      setTimeout(() => {
        textObj.set({ text: '1' })
        canvas.renderAll()
      }, 10)
    }
  })
  
  // 添加双击事件进入编辑模式
  textObj.on('mousedblclick', () => {
    textObj.enterEditing()
    textObj.selectAll()
  })
}

/**
 * 可编辑围栏类
 * 支持通过点击文本编辑尺寸，围栏会实时更新
 */
export class EditableFence {
  private map: FabricCanvas
  private fenceFactory: FenceFactory
  private imgInfoSize: Iposition
  private screenInfo: ScreenInfo
  private fenceObject: any = null
  private dimensionTexts: IText[] = []
  private dimensionLines: Line[] = []
  private centerMarker: Group | null = null
  private type: FenceType
  private id: string
  private onUpdateCallback?: (fenceData: FenceDate) => void
  private originalCanvasSelection: boolean = true // 保存画布原始选择状态

  constructor(
    map: FabricCanvas,
    imgInfoSize: Iposition,
    screenInfo: ScreenInfo,
    type: FenceType,
    id: string = 'editable-fence'
  ) {
    this.map = map
    this.fenceFactory = new FenceFactory(map.canvas)
    this.imgInfoSize = imgInfoSize
    this.screenInfo = screenInfo
    this.type = type
    this.id = id
    
    // 添加缩放监听器
    this.addZoomListener()
  }

  /**
   * 设置更新回调函数
   */
  setUpdateCallback(callback: (fenceData: FenceDate) => void): void {
    this.onUpdateCallback = callback
  }

  /**
   * 添加缩放监听器
   */
  private addZoomListener(): void {
    this.map.canvas.on('after:render', () => {
      this.updateElementsForZoom()
    })
  }

  /**
   * 创建圆形围栏
   */
  createCircleFence(center: { x: number; y: number }, radius: number): EditableFence {
    this.type = 'circle'
    
    // 创建圆形围栏
    this.fenceObject = this.fenceFactory.addCircleFence(center, radius)
    
    // 应用公共样式和圆形特定样式
    FenceUtils.applyCommonFenceStyle(this.fenceObject, this.id, true)
    this.fenceObject.set({
      lockRotation: true, // 锁定旋转
      lockUniScaling: true, // 锁定不均匀缩放，确保圆形保持圆形
      lockScalingX: false, // 允许X方向缩放
      lockScalingY: false, // 允许Y方向缩放
      lockScalingFlip: true, // 防止翻转
      originX: 'center', // 设置原点为中心
      originY: 'center', // 设置原点为中心
      centeredScaling: true, // 从中心点缩放
      centeredRotation: true, // 从中心点旋转
    })

    // 添加半径线和文本
    this.addRadiusDimension(center, radius)
    
    // 添加圆形调整事件监听
    this.addCircleResizeEvents()
    
    this.map.canvas.renderAll()
    return this
  }

  /**
   * 创建矩形围栏
   */
  createRectangleFence(position: { x: number; y: number }, width: number, height: number): EditableFence {
    this.type = 'rectangle'
    
    // 创建矩形围栏
    this.fenceObject = this.fenceFactory.addRectangleFence(position, width, height)
    
    // 应用公共样式和矩形特定样式
    FenceUtils.applyCommonFenceStyle(this.fenceObject, this.id, true)
    this.fenceObject.set({
      lockRotation: true, // 锁定旋转
      lockUniScaling: false, // 允许不均匀缩放，可以单独调整宽高
    })

    // 添加尺寸标注
    this.addRectangleDimensions(position, width, height)
    
    // 添加矩形调整事件监听
    this.addRectangleResizeEvents()
    
    this.map.canvas.renderAll()
    return this
  }

  /**
   * 创建多边形围栏
   */
  createPolygonFence(points: { x: number; y: number }[]): EditableFence {
    this.type = 'polygon'
    
    // 使用RealtimePolygonDrawer创建多边形围栏
    const polygonDrawer = new RealtimePolygonDrawer(this.map.canvas, this.fenceFactory)
    this.fenceObject = polygonDrawer.renderPolygon(points)
    
    if (this.fenceObject) {
      // 应用公共样式和多边形特定样式
      FenceUtils.applyCommonFenceStyle(this.fenceObject, this.id, true)
      this.fenceObject.set({
        lockRotation: false, // 多边形允许旋转
        noScaleCache: false,
        objectCaching: false
      })
    }

    this.map.canvas.renderAll()
    return this
  }

  /**
   * 获取围栏数据
   */
  getFenceData(): FenceDate {
    if (!this.fenceObject) {
      return { type: this.type, points: null }
    }
    
    return convertToFenceData(this.type, this.fenceObject, this.map, this.imgInfoSize, this.screenInfo)
  }

  /**
   * 销毁围栏及其所有相关元素
   */
  destroy(): void {
    // 移除围栏对象
    if (this.fenceObject) {
      this.map.canvas.remove(this.fenceObject)
    }
    
    // 移除尺寸文本
    this.dimensionTexts.forEach(text => {
      this.map.canvas.remove(text)
    })
    
    // 移除尺寸线
    this.dimensionLines.forEach(line => {
      this.map.canvas.remove(line)
    })
    
    // 移除圆心标记
    if (this.centerMarker) {
      this.map.canvas.remove(this.centerMarker)
    }
    
    // 恢复画布拖动功能
    this.restoreCanvasDragging()
    
    // 清空数组
    this.dimensionTexts = []
    this.dimensionLines = []
    this.centerMarker = null
    this.fenceObject = null
    
    this.map.canvas.renderAll()
  }

  /**
   * 设置围栏样式
   */
  setStyle(style: { fill?: string; stroke?: string; strokeWidth?: number }): void {
    if (this.fenceObject) {
      this.fenceObject.set(style)
      this.map.canvas.renderAll()
    }
  }

  /**
   * 获取围栏对象
   */
  getFenceObject(): any {
    return this.fenceObject
  }

  /**
   * 获取围栏工厂实例
   */
  getFenceFactory(): FenceFactory {
    return this.fenceFactory
  }

  /**
   * 更新元素以适应缩放变化
   */
  private updateElementsForZoom(): void {
    // 更新尺寸文本的字体大小和位置
    this.dimensionTexts.forEach(text => {
      const zoomStyle = FenceUtils.getZoomAdaptedStyle(this.map.canvas)
      text.set({
        fontSize: zoomStyle.fontSize,
        padding: zoomStyle.padding
      })
    })

    // 更新尺寸线的宽度
    this.dimensionLines.forEach(line => {
      const zoomStyle = FenceUtils.getZoomAdaptedStyle(this.map.canvas)
      line.set({
        strokeWidth: zoomStyle.strokeWidth
      })
    })
  }

  /**
   * 添加半径尺寸标注
   */
  private addRadiusDimension(center: { x: number; y: number }, radius: number): void {
    const zoomStyle = FenceUtils.getZoomAdaptedStyle(this.map.canvas)

    // 创建半径线
    const radiusLine = new Line(
      [center.x, center.y, center.x + radius, center.y],
      {
        stroke: '#409EFF',
        strokeWidth: zoomStyle.strokeWidth,
        selectable: false,
        evented: false
      }
    )

    // 创建半径文本
    const radiusText = FenceUtils.createStandardText(
      `${(radius * 2).toFixed(2)}`,
      {
        left: center.x + radius + 30 / this.map.canvas.getZoom(),
        top: center.y
      },
      this.map.canvas,
      {
        originX: 'left',
        originY: 'center'
      }
    )

    // 保存原始值和类型用于回退
    ;(radiusText as any).originalValue = radius * 2
    ;(radiusText as any).dimensionType = 'radius'

    // 添加文本编辑事件
    this.addTextEditEvents(radiusText, 'radius')

    this.dimensionLines.push(radiusLine)
    this.dimensionTexts.push(radiusText)
    this.map.canvas.add(radiusLine)
    this.map.canvas.add(radiusText)
  }

  /**
   * 添加矩形尺寸标注
   */
  private addRectangleDimensions(position: { x: number; y: number }, width: number, height: number): void {
    const zoomFactor = 1 / this.map.canvas.getZoom()

    // 使用完整的坐标转换获取实际宽度
    const realWidth = FenceUtils.batchCanvasToImage(
      { x: position.x, y: position.y, width: width },
      this.map,
      this.imgInfoSize,
      this.screenInfo
    ).width

    // 宽度标注
    const widthText = FenceUtils.createStandardText(
      `${realWidth.toFixed(2)}`,
      {
        left: position.x + width / 2,
        top: position.y - 15 * zoomFactor
      },
      this.map.canvas,
      {
        originX: 'center',
        originY: 'bottom'
      }
    )

    // 保存原始值和相关参数
    this.setTextDimensionProperties(widthText, realWidth, position, width, height, 'width')

    this.addTextEditEvents(widthText, 'width')
    this.dimensionTexts.push(widthText)
    this.map.canvas.add(widthText)

    // 使用完整的坐标转换获取实际高度
    const realHeight = FenceUtils.batchCanvasToImage(
      { x: position.x, y: position.y, height: height },
      this.map,
      this.imgInfoSize,
      this.screenInfo
    ).height

    // 高度标注
    const heightText = FenceUtils.createStandardText(
      `${realHeight.toFixed(2)}`,
      {
        left: position.x - 15 * zoomFactor,
        top: position.y + height / 2
      },
      this.map.canvas,
      {
        originX: 'right',
        originY: 'center',
        angle: 0
      }
    )

    // 保存原始值和相关参数
    this.setTextDimensionProperties(heightText, realHeight, position, width, height, 'height')

    this.addTextEditEvents(heightText, 'height')
    this.dimensionTexts.push(heightText)
    this.map.canvas.add(heightText)
  }

  /**
   * 设置文本尺寸属性的辅助方法
   */
  private setTextDimensionProperties(
    textObj: IText,
    originalValue: number,
    position: { x: number; y: number },
    width: number,
    height: number,
    dimensionType: string
  ): void {
    // 保存原始值用于回退
    ;(textObj as any).originalValue = originalValue

    // 保存原始视觉参数
    ;(textObj as any).visualDistance = 15
    ;(textObj as any).visualPadding = 12
    ;(textObj as any).visualFontSize = 15
    ;(textObj as any).rectX = position.x
    ;(textObj as any).rectY = position.y
    ;(textObj as any).rectWidth = width
    ;(textObj as any).rectHeight = height
    ;(textObj as any).dimensionType = dimensionType
  }

  /**
   * 添加文本编辑事件
   */
  private addTextEditEvents(textObj: IText, dimensionType: DimensionType): void {
    addCommonTextEditEvents(textObj, this.map.canvas, (newValue: number) => {
      this.updateFenceByDimension(dimensionType, newValue)
    })
  }

  /**
   * 根据尺寸更新围栏
   */
  private updateFenceByDimension(dimensionType: string, newValue: number): void {
    if (!this.fenceObject) return

    switch (this.type) {
      case 'circle':
        this.updateCircleDimension(dimensionType, newValue)
        break

      case 'rectangle':
        this.updateRectangleDimension(dimensionType, newValue)
        break

      case 'polygon':
        this.updatePolygonDimension(dimensionType, newValue)
        break
    }

    this.fenceObject.setCoords()
    this.map.canvas.renderAll()

    // 触发更新回调
    if (this.onUpdateCallback) {
      const fenceData = this.getFenceData()
      this.onUpdateCallback(fenceData)
    }
  }

  /**
   * 更新圆形尺寸
   */
  private updateCircleDimension(dimensionType: string, newValue: number): void {
    if (dimensionType !== 'radius') return

    // 获取当前圆形的实际坐标和尺寸
    const currentCircle = FenceUtils.batchCanvasToImage(
      {
        x: this.fenceObject.left,
        y: this.fenceObject.top,
        radius: this.fenceObject.radius
      },
      this.map,
      this.imgInfoSize,
      this.screenInfo
    )

    // 更新半径
    currentCircle.radius = newValue / 2

    // 将更新后的尺寸转换回画布坐标
    const newCanvasData = FenceUtils.batchImageToCanvas(
      currentCircle,
      this.map,
      this.imgInfoSize,
      this.screenInfo
    )

    this.fenceObject.set({
      radius: newCanvasData.radius,
      width: newCanvasData.radius * 2,
      height: newCanvasData.radius * 2
    })

    // 更新半径线和文本
    this.updateRadiusDimension(
      { x: this.fenceObject.left, y: this.fenceObject.top },
      newCanvasData.radius
    )
  }

  /**
   * 更新矩形尺寸
   */
  private updateRectangleDimension(dimensionType: string, newValue: number): void {
    if (dimensionType !== 'width' && dimensionType !== 'height') return

    // 获取当前矩形的实际坐标和尺寸
    const currentRect = FenceUtils.batchCanvasToImage(
      {
        x: this.fenceObject.left,
        y: this.fenceObject.top,
        width: this.fenceObject.width,
        height: this.fenceObject.height
      },
      this.map,
      this.imgInfoSize,
      this.screenInfo
    )

    // 更新对应的尺寸
    if (dimensionType === 'width') {
      currentRect.width = newValue
    } else if (dimensionType === 'height') {
      currentRect.height = newValue
    }

    // 将更新后的尺寸转换回画布坐标
    const newCanvasData = FenceUtils.batchImageToCanvas(
      currentRect,
      this.map,
      this.imgInfoSize,
      this.screenInfo
    )

    this.fenceObject.set({
      width: newCanvasData.width,
      height: newCanvasData.height
    })

    this.updateRectangleDimensions()
  }

  /**
   * 更新多边形尺寸
   */
  private updatePolygonDimension(dimensionType: string, newValue: number): void {
    if (dimensionType.startsWith('segment-')) {
      // 多边形边长修改比较复杂，直接显示提示消息并更新显示
      this.updateDimensionDisplay()
    }
  }

  /**
   * 更新半径尺寸显示
   */
  private updateRadiusDimension(center: { x: number; y: number }, radius: number): void {
    if (this.dimensionLines[0]) {
      this.dimensionLines[0].set({
        x1: center.x,
        y1: center.y,
        x2: center.x + radius,
        y2: center.y
      })
    }

    if (this.dimensionTexts[0]) {
      const zoomFactor = 1 / this.map.canvas.getZoom()
      this.dimensionTexts[0].set({
        left: center.x + radius + 30 * zoomFactor,
        top: center.y,
        text: `${(radius * 2).toFixed(2)}`
      })
      this.dimensionTexts[0].setCoords()
    }
  }

  /**
   * 更新矩形尺寸显示
   */
  private updateRectangleDimensions(): void {
    if (!this.fenceObject) return

    // 获取矩形的实际位置和尺寸（考虑缩放因素）
    const position = {
      x: this.fenceObject.left,
      y: this.fenceObject.top
    }
    const width = this.fenceObject.getScaledWidth()
    const height = this.fenceObject.getScaledHeight()
    const zoomFactor = 1 / this.map.canvas.getZoom()
    const visualDistance = 15

    // 更新宽度文本
    if (this.dimensionTexts[0]) {
      this.updateSingleDimensionText(
        this.dimensionTexts[0],
        { x: position.x, y: position.y, width: width },
        'width',
        {
          left: position.x + width / 2,
          top: position.y - visualDistance * zoomFactor
        },
        position,
        width,
        height
      )
    }

    // 更新高度文本
    if (this.dimensionTexts[1]) {
      this.updateSingleDimensionText(
        this.dimensionTexts[1],
        { x: position.x, y: position.y, height: height },
        'height',
        {
          left: position.x - visualDistance * zoomFactor,
          top: position.y + height / 2
        },
        position,
        width,
        height
      )
    }

    // 强制重新渲染
    this.map.canvas.requestRenderAll()
  }

  /**
   * 更新单个尺寸文本的辅助方法
   */
  private updateSingleDimensionText(
    textObj: IText,
    canvasData: any,
    dimensionKey: string,
    newPosition: { left: number; top: number },
    rectPosition: { x: number; y: number },
    width: number,
    height: number
  ): void {
    // 使用完整的坐标转换获取实际尺寸
    const realValue = FenceUtils.batchCanvasToImage(
      canvasData,
      this.map,
      this.imgInfoSize,
      this.screenInfo
    )[dimensionKey]

    // 更新保存的位置信息
    this.setTextDimensionProperties(textObj, realValue, rectPosition, width, height, dimensionKey)

    textObj.set({
      left: newPosition.left,
      top: newPosition.top,
      text: `${realValue.toFixed(2)}`
    })
    textObj.setCoords()
  }

  /**
   * 更新尺寸显示
   */
  private updateDimensionDisplay(): void {
    // 根据围栏类型更新相应的尺寸显示
    switch (this.type) {
      case 'circle':
        if (this.fenceObject) {
          this.updateRadiusDimension(
            { x: this.fenceObject.left, y: this.fenceObject.top },
            this.fenceObject.radius
          )
        }
        break
      case 'rectangle':
        this.updateRectangleDimensions()
        break
      case 'polygon':
        // 多边形的尺寸显示更新
        console.log('多边形尺寸显示更新')
        break
    }
  }

  /**
   * 添加圆形调整事件监听
   */
  private addCircleResizeEvents(): void {
    if (!this.fenceObject) return

    this.fenceObject.on('scaling', () => {
      const newRadius = this.fenceObject.getScaledWidth() / 2
      this.updateRadiusDimension(
        { x: this.fenceObject.left, y: this.fenceObject.top },
        newRadius
      )
    })

    this.fenceObject.on('moving', () => {
      const newRadius = this.fenceObject.getScaledWidth() / 2
      this.updateRadiusDimension(
        { x: this.fenceObject.left, y: this.fenceObject.top },
        newRadius
      )
    })
  }

  /**
   * 添加矩形调整事件监听
   */
  private addRectangleResizeEvents(): void {
    if (!this.fenceObject) return

    this.fenceObject.on('scaling', () => {
      this.updateRectangleDimensions()
    })

    this.fenceObject.on('moving', () => {
      this.updateRectangleDimensions()
    })
  }

  /**
   * 恢复画布拖动功能
   */
  private restoreCanvasDragging(): void {
    // 恢复画布的选择功能
    this.map.canvas.selection = this.originalCanvasSelection
  }
}
